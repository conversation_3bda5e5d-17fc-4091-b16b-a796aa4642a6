import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { Button, Input, Label, Card, CardContent, CardHeader } from '@satur/ui';
import { useState, useEffect } from 'react';
import { api } from '../lib/api';

const resetPasswordSchema = z.object({
  newPassword: z.string().min(6, { message: 'A senha deve ter no mínimo 6 caracteres' }),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'As senhas não coincidem',
  path: ['confirmPassword'],
});

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get('token');

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (!token) {
      setError('Token de redefinição não encontrado.');
    }
  }, [token]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!token) return;

    setIsLoading(true);
    setMessage('');
    setError('');

    try {
      await api.post('/auth/reset-password', { token, newPassword: data.newPassword });
      setMessage('✅ Senha redefinida com sucesso! Você será redirecionado para o login em 3 segundos.');
      setTimeout(() => navigate('/login'), 3000);
    } catch (err: any) {
      console.error('Reset password error:', err);
      if (err.response?.status === 400) {
        setError('❌ Token inválido ou expirado. Por favor, solicite um novo link de redefinição.');
      } else if (err.response?.status === 429) {
        setError('❌ Muitas tentativas. Aguarde alguns minutos antes de tentar novamente.');
      } else {
        setError('❌ Ocorreu um erro ao redefinir a senha. Tente novamente mais tarde.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-8 pt-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Redefinir Senha
            </h1>
            <p className="text-gray-600 text-sm">
              Crie uma nova senha para sua conta.
            </p>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            {message && <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg text-sm text-center mb-4">{message}</div>}
            {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm text-center mb-4">{error}</div>}

            {!message && !error && token && (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="newPassword" className="text-sm font-medium text-gray-700">
                    Nova Senha
                  </Label>
                  <Input 
                    id="newPassword" 
                    type="password" 
                    placeholder="Digite sua nova senha"
                    {...register('newPassword')}
                    className={`h-12 border-gray-200 focus:border-blue-700 focus:ring-blue-700 ${
                      errors.newPassword ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                  />
                  {errors.newPassword && (
                    <p className="text-sm text-red-500 mt-1">{errors.newPassword.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                    Confirmar Nova Senha
                  </Label>
                  <Input 
                    id="confirmPassword" 
                    type="password" 
                    placeholder="Confirme sua nova senha"
                    {...register('confirmPassword')}
                    className={`h-12 border-gray-200 focus:border-blue-700 focus:ring-blue-700 ${
                      errors.confirmPassword ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                  />
                  {errors.confirmPassword && (
                    <p className="text-sm text-red-500 mt-1">{errors.confirmPassword.message}</p>
                  )}
                </div>
                <Button 
                  type="submit" 
                  disabled={isLoading} 
                  className="w-full h-12 bg-blue-700 hover:bg-blue-800 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Salvando...
                    </div>
                  ) : (
                    'Salvar Nova Senha'
                  )}
                </Button>
              </form>
            )}

            {!token && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm text-center">
                Link de redefinição inválido ou expirado. 
                <Link to="/forgot-password" className="font-medium hover:underline ml-1">
                  Solicite um novo link
                </Link>
              </div>
            )}

            {(message || error) && (
                 <div className="text-center mt-6">
                    <Link
                      to="/login"
                      className="text-blue-700 hover:text-blue-800 font-medium hover:underline transition-colors"
                    >
                      Voltar para o Login
                    </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
