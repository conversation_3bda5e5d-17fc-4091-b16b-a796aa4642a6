import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Link } from 'react-router-dom';
import { z } from 'zod';
import { Button, Input, Label, Card, CardContent, CardHeader } from '@satur/ui';
import { useState } from 'react';
import { api } from '../lib/api';

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Email inválido' }),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    setMessage('');
    try {
      await api.post('/auth/forgot-password', data);
      setMessage('✅ Se um usuário com este email existir, um link de redefinição de senha será enviado. Verifique sua caixa de entrada.');
    } catch (error: any) {
      console.error('Forgot password error:', error);
      if (error.response?.status === 429) {
        setMessage('❌ Muitas tentativas. Aguarde alguns minutos antes de tentar novamente.');
      } else {
        setMessage('❌ Ocorreu um erro. Tente novamente mais tarde.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-8 pt-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Esqueceu sua Senha?
            </h1>
            <p className="text-gray-600 text-sm">
              Digite seu email para receber o link de redefinição.
            </p>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            {message ? (
              <div className={`px-4 py-3 rounded-lg text-sm text-center ${
                message.includes('✅') 
                  ? 'bg-green-50 border border-green-200 text-green-700' 
                  : 'bg-red-50 border border-red-200 text-red-700'
              }`}>
                {message}
              </div>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Digite seu email"
                    {...register('email')}
                    className={`h-12 border-gray-200 focus:border-blue-700 focus:ring-blue-700 ${
                      errors.email ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 bg-blue-700 hover:bg-blue-800 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Enviando...' : 'Enviar Link'}
                </Button>
              </form>
            )}

            <div className="text-center mt-6">
              <p className="text-sm text-gray-600">
                Lembrou a senha?{' '}
                <Link
                  to="/login"
                  className="text-blue-700 hover:text-blue-800 font-medium hover:underline transition-colors"
                >
                  Fazer Login
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
