import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const ResetPasswordSchema = z
  .object({
    token: z.string().min(1, 'Token é obrigatório'),
    newPassword: z.string().min(6, 'A senha deve ter no mínimo 6 caracteres'),
    confirmPassword: z.string().optional(),
  })
  .refine(
    (data) => {
      // Only validate confirmation if provided
      if (data.confirmPassword !== undefined) {
        return data.newPassword === data.confirmPassword;
      }
      return true;
    },
    {
      message: 'As senhas não coincidem',
      path: ['confirmPassword'],
    },
  );

export class ResetPasswordDto extends createZodDto(ResetPasswordSchema) {}
