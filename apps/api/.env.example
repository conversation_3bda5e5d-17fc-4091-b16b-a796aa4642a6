# Configuração do Banco de Dados
DATABASE_URL="postgresql://user:password@host:port/db"

# Mailtrap Credentials
MAIL_HOST="sandbox.smtp.mailtrap.io"
MAIL_PORT=2525
MAIL_USER="b826b82af25bbb"
MAIL_PASS="d730a0c9a282a2"
MAIL_FROM="'No Reply' <<EMAIL>>"

# Configuração JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Configuração do Servidor
PORT=3000
NODE_ENV="development"

# Configuração do PostgreSQL (para Docker)
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=carlao_db
POSTGRES_PORT=5433
